
import { GoogleGenAI, GenerateContentResponse, Part, GenerateContentParameters, GroundingChunk } from "@google/genai";
import { GEMINI_MODEL_TEXT } from '../constants';

const API_KEY = (import.meta.env as any).VITE_GEMINI_API_KEY;
let aiClientInstance: GoogleGenAI | null = null;

function getInitializedAiClient(): GoogleGenAI | null {
  if (!API_KEY) {
    if (!aiClientInstance && (typeof console !== 'undefined' && console.error)) { 
        console.error("Gemini API Key (API_KEY) is not set in environment variables. Gemini service cannot be initialized.");
    }
    return null;
  }

  if (!aiClientInstance) {
    try {
      aiClientInstance = new GoogleGenAI({ apiKey: API_KEY });
    } catch (error) {
      if (typeof console !== 'undefined' && console.error) {
        console.error("Failed to initialize GoogleGenAI client:", error);
      }
      return null;
    }
  }
  return aiClientInstance;
}

interface GeminiResponse {
  text: string;
  sources?: GroundingChunk[];
}

export const generateGeminiResponse = async (
  prompt: string, // User's text input, can be empty.
  systemInstruction: string,
  documentContext?: string | null,
  useGoogleSearch: boolean = false,
  audioData?: { mimeType: string; data: string; } // Added audioData
): Promise<GeminiResponse> => {
  const ai = getInitializedAiClient();

  if (!ai) {
    return { text: "Error: Gemini API client is not initialized. Please ensure the API_KEY is correctly configured." };
  }

  if (!prompt.trim() && !audioData) {
      return { text: "Error: No input provided (neither text nor audio)." };
  }

  try {
    const requestParts: Part[] = [];
    let textForGeminiPart: string;

    if (audioData && !prompt.trim()) {
      textForGeminiPart = "Process and respond to the following audio."; // Default prompt if only audio
    } else {
      textForGeminiPart = prompt; // User's text or text accompanying audio
    }

    if (documentContext) {
      // Prepend document context to the textual part
      requestParts.push({ text: `CONTEXT DOCUMENT:\n\`\`\`\n${documentContext}\n\`\`\`\n\nUSER QUESTION/REQUEST (based on text/audio that follows):\n${textForGeminiPart}` });
    } else {
      requestParts.push({ text: textForGeminiPart });
    }

    // Add audio data if present
    if (audioData) {
      requestParts.push({ inlineData: { mimeType: audioData.mimeType, data: audioData.data } });
    }
    
    const requestPayload: GenerateContentParameters = {
      model: GEMINI_MODEL_TEXT,
      contents: [{ role: "user", parts: requestParts }],
      config: {
        systemInstruction: systemInstruction,
        temperature: 0.7,
        topP: 0.95,
        topK: 64,
      }
    };

    if (useGoogleSearch) {
        if (!requestPayload.config) requestPayload.config = {};
        requestPayload.config.tools = [{googleSearch: {}}];
        if (requestPayload.config.responseMimeType === "application/json") {
            delete requestPayload.config.responseMimeType;
        }
    }
    
    const response: GenerateContentResponse = await ai.models.generateContent(requestPayload);
    
    const responseText = response.text;
    const groundingMetadata = response.candidates?.[0]?.groundingMetadata;
    const sources = groundingMetadata?.groundingChunks || [];
    
    return { text: responseText, sources: sources };

  } catch (error) {
    console.error('Gemini API Error:', error);
    if (error instanceof Error) {
        if (error.message.includes("API key not valid") || error.message.includes("API_KEY_INVALID")) {
             return { text: "Error: Invalid Gemini API Key. Please check your configuration." };
        }
         return { text: `Error communicating with Gemini: ${error.message}` };
    }
    return { text: 'An unknown error occurred while contacting Gemini.' };
  }
};
