
import React from 'react';
import { GroundingChunk } from '../types';

interface SourceDisplayProps {
  sources: GroundingChunk[];
}

const SourceDisplay: React.FC<SourceDisplayProps> = ({ sources }) => {
  if (!sources || sources.length === 0) {
    return null;
  }

  const validSources = sources.filter(source => 
    (source.web && source.web.uri && source.web.title) || 
    (source.retrievedContext && source.retrievedContext.uri && source.retrievedContext.title)
  );

  if (validSources.length === 0) {
    return null;
  }

  return (
    <div className="mt-2 pt-2 border-t border-neutral-500">
      <h4 className="text-xs font-semibold text-gray-300 mb-1">Sources:</h4>
      <ul className="list-disc list-inside space-y-1">
        {validSources.map((source, index) => {
          const S = source.web || source.retrievedContext; // Prioritize web if both exist, though unlikely
          if (!S || !S.uri || !S.title) return null;
          
          // Truncate long titles
          const displayTitle = S.title.length > 80 ? S.title.substring(0, 77) + "..." : S.title;

          return (
            <li key={index} className="text-xs">
              <a
                href={S.uri}
                target="_blank"
                rel="noopener noreferrer"
                className="text-teal-400 hover:text-teal-300 hover:underline"
                title={S.title}
              >
                {displayTitle}
              </a>
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default SourceDisplay;