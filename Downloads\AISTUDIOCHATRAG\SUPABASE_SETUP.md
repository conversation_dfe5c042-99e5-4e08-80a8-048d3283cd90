# Configuração do Supabase

## 🎯 Visão Geral

O Supabase é usado para persistir:
- ✅ **Conversas do chat** com o bot Gemini
- ✅ **Mensagens** das conversas (com fontes RAG)
- ✅ **Contatos do WhatsApp** 
- ✅ **Mensagens do WhatsApp** (enviadas/recebidas)
- ✅ **Busca e filtros** em tempo real
- ✅ **Histórico completo** de interações

## 🚀 Passo a Passo

### 1. Criar Conta no Supabase

1. Acesse: https://supabase.com
2. Clique em **"Start your project"**
3. Faça login com GitHub, Google ou email
4. Crie uma nova organização (se necessário)

### 2. Criar Novo Projeto

1. Clique em **"New Project"**
2. Escolha sua organização
3. Preencha:
   - **Name**: `gemini-rag-chat` (ou nome de sua escolha)
   - **Database Password**: Crie uma senha forte
   - **Region**: Escolha a região mais próxima
4. Clique em **"Create new project"**
5. Aguarde a criação (2-3 minutos)

### 3. Obter Credenciais

Após a criação do projeto:

1. Vá para **Settings** → **API**
2. Copie as seguintes informações:
   - **Project URL** (ex: `https://abcdefgh.supabase.co`)
   - **anon public** key (chave pública)
   - **service_role** key (chave privada - **CUIDADO!**)

### 4. Configurar Variáveis de Ambiente

**Backend (.env):**
```env
PORT=3001
FRONTEND_URL=http://localhost:5173
SESSION_NAME=whatsapp-session

# Supabase Configuration
SUPABASE_URL=https://seu-projeto.supabase.co
SUPABASE_ANON_KEY=sua_anon_key_aqui
SUPABASE_SERVICE_ROLE_KEY=sua_service_role_key_aqui
```

**Frontend (.env.local):**
```env
GEMINI_API_KEY=sua_chave_gemini_aqui

# Supabase Configuration
VITE_SUPABASE_URL=https://seu-projeto.supabase.co
VITE_SUPABASE_ANON_KEY=sua_anon_key_aqui
```

### 5. Criar Schema do Banco

1. No painel do Supabase, vá para **SQL Editor**
2. Clique em **"New query"**
3. Copie e cole o conteúdo do arquivo `database/schema.sql`
4. Clique em **"Run"** para executar

Ou execute manualmente:

```sql
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Tabela de conversas do chat com o bot
CREATE TABLE IF NOT EXISTS conversations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id TEXT,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Tabela de mensagens das conversas
CREATE TABLE IF NOT EXISTS messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    sender TEXT NOT NULL CHECK (sender IN ('user', 'bot', 'system')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb,
    sources JSONB DEFAULT '[]'::jsonb
);

-- Tabela de contatos do WhatsApp
CREATE TABLE IF NOT EXISTS whatsapp_contacts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    phone_number TEXT NOT NULL UNIQUE,
    name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_message_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Tabela de mensagens do WhatsApp
CREATE TABLE IF NOT EXISTS whatsapp_messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    contact_id UUID REFERENCES whatsapp_contacts(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    sender TEXT NOT NULL CHECK (sender IN ('user', 'bot')),
    message_type TEXT DEFAULT 'text',
    whatsapp_message_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_conversations_created_at ON conversations(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_whatsapp_contacts_phone ON whatsapp_contacts(phone_number);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_contact_id ON whatsapp_messages(contact_id);

-- RLS (Row Level Security)
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE whatsapp_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE whatsapp_messages ENABLE ROW LEVEL SECURITY;

-- Políticas básicas (permitir tudo por enquanto)
CREATE POLICY "Allow all operations on conversations" ON conversations FOR ALL USING (true);
CREATE POLICY "Allow all operations on messages" ON messages FOR ALL USING (true);
CREATE POLICY "Allow all operations on whatsapp_contacts" ON whatsapp_contacts FOR ALL USING (true);
CREATE POLICY "Allow all operations on whatsapp_messages" ON whatsapp_messages FOR ALL USING (true);
```

### 6. Verificar Configuração

1. Reinicie o backend: `Ctrl+C` e `npm start`
2. Reinicie o frontend: `Ctrl+C` e `npm run dev`
3. Verifique os logs - não deve haver erros do Supabase
4. Teste criando uma conversa no chat

## 🔧 Funcionalidades Disponíveis

### Com Supabase Configurado:
- ✅ **Auto-save** de conversas
- ✅ **Histórico** de conversas navegável
- ✅ **Busca** em mensagens e conversas
- ✅ **Contatos WhatsApp** salvos automaticamente
- ✅ **Histórico WhatsApp** completo
- ✅ **Estatísticas** de uso
- ✅ **Backup** automático de dados

### Sem Supabase:
- ✅ **Chat funcional** (apenas sessão atual)
- ✅ **WhatsApp** funcional
- ✅ **RAG e Google Search** funcionais
- ❌ Sem persistência de dados
- ❌ Sem histórico
- ❌ Sem busca

## 🛠️ Solução de Problemas

### Erro: "Invalid URL"
- Verifique se `SUPABASE_URL` está correto
- URL deve começar com `https://`
- Não deve ter `/` no final

### Erro: "Invalid API key"
- Verifique se as chaves estão corretas
- `anon key` para frontend
- `service_role key` para backend
- Chaves são diferentes!

### Erro: "relation does not exist"
- Execute o script SQL do schema
- Verifique se todas as tabelas foram criadas
- Vá para **Database** → **Tables** no Supabase

### Erro: "RLS policy violation"
- Verifique se as políticas RLS foram criadas
- Para desenvolvimento, use políticas permissivas
- Para produção, implemente autenticação

## 📊 Monitoramento

### No Painel Supabase:
- **Database** → **Tables**: Ver dados
- **Database** → **Logs**: Ver logs SQL
- **API** → **Logs**: Ver logs da API
- **Auth** → **Users**: Gerenciar usuários (futuro)

### Logs da Aplicação:
- Backend: Console e arquivos em `backend/logs/`
- Frontend: Console do navegador (F12)

## 🔒 Segurança

### Desenvolvimento:
- Use políticas RLS permissivas
- Mantenha `service_role key` segura
- Não commite chaves no Git

### Produção:
- Implemente autenticação real
- Configure políticas RLS restritivas
- Use variáveis de ambiente seguras
- Configure CORS adequadamente

## 📈 Próximos Passos

1. **Autenticação**: Implementar login de usuários
2. **Multi-tenant**: Separar dados por usuário
3. **Backup**: Configurar backups automáticos
4. **Analytics**: Dashboards de uso
5. **API**: Endpoints públicos para integração

---

**🎉 Parabéns! Sua integração Supabase está pronta!**

Com o Supabase configurado, você terá persistência completa de dados e funcionalidades avançadas de histórico e busca.
