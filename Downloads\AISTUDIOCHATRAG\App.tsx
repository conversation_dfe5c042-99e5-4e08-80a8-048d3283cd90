
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { ChatMessage, WhatsAppStatus } from './types'; // Added WhatsAppStatus
import ChatInterface from './components/ChatInterface';
import DocumentInput from './components/DocumentInput';
import WhatsAppControls from './components/WhatsAppControls'; // Added
import { generateGeminiResponse } from './services/geminiService';
import { SYSTEM_INSTRUCTION_RAG, SYSTEM_INSTRUCTION_GENERAL } from './constants';

interface SendMessagePayload {
  text?: string;
  audio?: {
    mimeType: string;
    data: string; // base64
  };
}

const App: React.FC = () => {
  const [apiKeyLoaded, setApiKeyLoaded] = useState<boolean>(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [documentContent, setDocumentContent] = useState<string | null>(null);
  const [useGoogleSearch, setUseGoogleSearch] = useState<boolean>(false);
  
  // WhatsApp State
  const [lastBotResponseText, setLastBotResponseText] = useState<string | null>(null);

  const chatInterfaceRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (process.env.API_KEY) {
      setApiKeyLoaded(true);
      setMessages([
        { 
          id: 'initial-system-message', 
          text: "Welcome! I'm your RAG-enabled Chatbot. You can upload a document, use your microphone for audio input, or ask general questions. Check the sidebar for document loading, Google Search toggle, and WhatsApp integration (mock).", 
          sender: 'system', 
          timestamp: new Date() 
        }
      ]);
    } else {
      setMessages([
        { 
          id: 'error-system-message', 
          text: "ERROR: API_KEY is not configured. Please set the API_KEY environment variable for the core chatbot functionality.", 
          sender: 'system', 
          timestamp: new Date() 
        }
      ]);
    }
  }, []);

  const handleDocumentLoad = useCallback((content: string) => {
    setDocumentContent(content);
    setMessages(prev => [...prev, {
      id: `doc-loaded-${Date.now()}`,
      text: content ? `Document loaded (${(content.length / 1024).toFixed(2)} KB). I will now use this document as primary context.` : 'Document context cleared.',
      sender: 'system',
      timestamp: new Date()
    }]);
  }, []); // Dependency array changed to []

  const handleSendMessage = useCallback(async (payload: SendMessagePayload) => {
    const { text: userInput, audio: audioInput } = payload;

    if (!userInput?.trim() && !audioInput) { // Must have text or audio
      console.warn("Attempted to send empty message.");
      return;
    }
    if (!apiKeyLoaded) return;

    let displayUserMessageText: string;
    if (audioInput) {
      displayUserMessageText = `[Audio Input${userInput?.trim() ? `: "${userInput.trim()}"` : ''}]`;
    } else {
      displayUserMessageText = userInput!;
    }

    const newUserMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      text: displayUserMessageText,
      sender: 'user',
      timestamp: new Date()
    };
    setMessages(prev => [...prev, newUserMessage]);
    setIsLoading(true);
    setLastBotResponseText(null); // Clear previous bot response before new one arrives

    try {
      const systemInstruction = documentContent ? SYSTEM_INSTRUCTION_RAG : SYSTEM_INSTRUCTION_GENERAL;
      const result = await generateGeminiResponse(
        userInput || "", // Pass text prompt, can be empty if only audio
        systemInstruction,
        documentContent,
        useGoogleSearch,
        audioInput     // Pass audio data
      );
      
      const botMessage: ChatMessage = {
        id: `bot-${Date.now()}`,
        text: result.text,
        sender: 'bot',
        timestamp: new Date(),
        sources: result.sources
      };
      setMessages(prev => [...prev, botMessage]);
      setLastBotResponseText(result.text); // Store bot response for WhatsApp

    } catch (error) {
      console.error("Error generating response:", error);
      const errorMessageText = `Sorry, I encountered an error. ${error instanceof Error ? error.message : 'Please try again.'}`;
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}`,
        text: errorMessageText,
        sender: 'bot',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
      setLastBotResponseText(errorMessageText); // Also store error as last "bot" response
    } finally {
      setIsLoading(false);
    }
  }, [apiKeyLoaded, documentContent, useGoogleSearch]);

  return (
    <div className="flex flex-col h-screen font-sans bg-neutral-800 text-white">
      <header className="bg-teal-600 p-4 text-white shadow-md">
        <h1 className="text-2xl font-semibold text-center">Gemini RAG Chat (WhatsApp Style)</h1>
      </header>
      
      {!apiKeyLoaded && (
        <div className="p-4 bg-red-700 text-white text-center">
          API Key not found. Chatbot functionality disabled. Please ensure API_KEY environment variable is set.
        </div>
      )}

      <div className="flex flex-1 overflow-hidden">
        <aside className="w-1/3 min-w-[320px] max-w-[480px] bg-neutral-700 p-4 border-r border-neutral-600 flex flex-col space-y-4 overflow-y-auto">
          <DocumentInput onDocumentLoad={handleDocumentLoad} />
          
          <div className="bg-neutral-600 p-3 rounded-lg shadow">
            <label htmlFor="googleSearchToggle" className="flex items-center cursor-pointer">
              <input 
                type="checkbox" 
                id="googleSearchToggle"
                checked={useGoogleSearch}
                onChange={(e) => setUseGoogleSearch(e.target.checked)}
                className="sr-only peer" 
              />
              <div className="relative w-11 h-6 bg-gray-400 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-500"></div>
              <span className="ml-3 text-sm font-medium text-gray-200">Use Google Search</span>
            </label>
            {useGoogleSearch && documentContent && (
              <p className="text-xs text-yellow-400 mt-2">Note: Google Search will be used with document context.</p>
            )}
             {useGoogleSearch && !documentContent && (
              <p className="text-xs text-teal-300 mt-2">Google Search will be used for general queries.</p>
            )}
          </div>

          {documentContent && (
            <div className="bg-neutral-600 p-3 rounded-lg shadow">
              <h3 className="text-md font-semibold mb-1 text-teal-400">Active Document Context</h3>
              <p className="text-xs text-gray-300">
                A document is loaded. ({ (documentContent.length / 1024).toFixed(2)} KB)
              </p>
            </div>
          )}

          {/* WhatsApp Controls Section */}
          <WhatsAppControls lastBotResponseText={lastBotResponseText} />

        </aside>

        <main 
          ref={chatInterfaceRef} 
          className="flex-1 flex flex-col bg-cover bg-center"
          style={{ backgroundImage: "url('https://picsum.photos/seed/whatsappbg/1200/900')" }} 
        >
          <ChatInterface 
            messages={messages} 
            onSendMessage={handleSendMessage} 
            isLoading={isLoading}
            apiKeyLoaded={apiKeyLoaded}
          />
        </main>
      </div>
    </div>
  );
};

export default App;
