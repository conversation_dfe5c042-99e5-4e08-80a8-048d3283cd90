
export interface GroundingChunkWeb {
  uri?: string;
  title?: string;
}

export interface GroundingChunkRetrievedContext {
  uri?: string; // Changed from uri: string to uri?: string
  title?: string; // Also made title optional for consistency with @google/genai if it's optional there too.
}
export interface GroundingChunk {
  web?: GroundingChunkWeb;
  retrievedContext?: GroundingChunkRetrievedContext;
  // Add other potential grounding chunk types if needed
}


export interface ChatMessage {
  id: string;
  text: string;
  sender: 'user' | 'bot' | 'system';
  timestamp: Date;
  sources?: GroundingChunk[];
}

// Ensure process.env can be extended for API_KEY
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      API_KEY?: string;
    }
  }
}

// WhatsApp Integration Types
export interface WhatsAppMessage {
  id: string;
  text: string;
  sender: string; // Phone number or contact name
  timestamp: Date;
  isFromMe: boolean; // True if sent from our "bot" via WhatsApp, false if incoming
}

export type WhatsAppStatus = 'disconnected' | 'connecting_qr' | 'connected' | 'error' | 'loading' | 'requires_backend';