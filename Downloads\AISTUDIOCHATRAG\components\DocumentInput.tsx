
import React, { useState, useCallback } from 'react';

interface DocumentInputProps {
  onDocumentLoad: (content: string) => void;
}

const DocumentInput: React.FC<DocumentInputProps> = ({ onDocumentLoad }) => {
  const [textInput, setTextInput] = useState('');
  const [fileName, setFileName] = useState<string | null>(null);
  const [isProcessingFile, setIsProcessingFile] = useState<boolean>(false);
  const [processingMessage, setProcessingMessage] = useState<string | null>(null);


  const handleFileChange = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setIsProcessingFile(true);
      setFileName(null); // Clear previous file name
      setTextInput(''); // Clear previous text input
      setProcessingMessage(`Processing ${file.name}...`);
      try {
        if (file.type === 'application/pdf') {
          const pdfjsLib = await import('pdfjs-dist/build/pdf.min.mjs');
          // Set the workerSrc to the full CDN URL of the worker script
          pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://esm.sh/pdfjs-dist@4.5.136/build/pdf.worker.min.mjs';

          const arrayBuffer = await file.arrayBuffer();
          const pdf = await pdfjsLib.getDocument(arrayBuffer).promise;
          let fullText = '';
          for (let i = 1; i <= pdf.numPages; i++) {
            const page = await pdf.getPage(i);
            const textContent = await page.getTextContent();
            fullText += textContent.items.map((item: any) => item.str).join(' ') + '\n';
          }
          setTextInput(fullText);
          setFileName(file.name);
          onDocumentLoad(fullText);
          setProcessingMessage(`${file.name} loaded successfully.`);
        } else if (file.type === 'text/plain') {
          const reader = new FileReader();
          reader.onload = (e) => {
            const content = e.target?.result as string;
            setTextInput(content);
            setFileName(file.name);
            onDocumentLoad(content);
            setProcessingMessage(`${file.name} loaded successfully.`);
          };
          reader.readAsText(file);
        } else {
          alert('Unsupported file type. Please upload a .txt or .pdf file.');
          setProcessingMessage(null);
        }
      } catch (error) {
        console.error("Error processing file:", error);
        alert(`Error processing file: ${error instanceof Error ? error.message : 'Unknown error'}`);
        setProcessingMessage(`Error loading ${file.name}.`);
      } finally {
        // Keep message for a bit or clear it:
        // setTimeout(() => setProcessingMessage(null), 3000); 
        setIsProcessingFile(false);
      }
    }
  }, [onDocumentLoad]);

  const handlePasteLoad = useCallback(() => {
    if (textInput.trim()) {
      onDocumentLoad(textInput);
      setFileName('Pasted Content');
      setProcessingMessage('Pasted content loaded.');
    } else {
        alert("Please paste some content before loading.")
    }
  }, [textInput, onDocumentLoad]);
  
  const handleClear = useCallback(() => {
    setTextInput('');
    setFileName(null);
    onDocumentLoad(''); 
    setProcessingMessage('Document context cleared.');
    const fileInput = document.getElementById('file-upload') as HTMLInputElement;
    if(fileInput) fileInput.value = '';
     // setTimeout(() => setProcessingMessage(null), 2000);

  }, [onDocumentLoad]);

  return (
    <div className="bg-neutral-600 p-4 rounded-lg shadow space-y-3">
      <h2 className="text-lg font-semibold text-teal-400 mb-2">Load Document Context</h2>
      
      <div>
        <label htmlFor="file-upload" className="block text-sm font-medium text-gray-300 mb-1">
          Upload .txt or .pdf file
        </label>
        <input
          id="file-upload"
          type="file"
          accept=".txt,.pdf"
          onChange={handleFileChange}
          disabled={isProcessingFile}
          className="block w-full text-sm text-gray-300
                     file:mr-4 file:py-2 file:px-4
                     file:rounded-full file:border-0
                     file:text-sm file:font-semibold
                     file:bg-teal-500 file:text-white
                     hover:file:bg-teal-600
                     disabled:opacity-50 disabled:cursor-not-allowed"
        />
      </div>
      
      <div className="text-center text-sm text-gray-400">OR</div>

      <div>
        <label htmlFor="text-area-input" className="block text-sm font-medium text-gray-300 mb-1">
          Paste text content
        </label>
        <textarea
          id="text-area-input"
          rows={6}
          value={textInput}
          onChange={(e) => setTextInput(e.target.value)}
          placeholder="Paste your document text here..."
          disabled={isProcessingFile}
          className="w-full p-2 border border-neutral-500 rounded-md bg-neutral-700 text-white placeholder-gray-400 focus:ring-2 focus:ring-teal-500 focus:border-teal-500 disabled:opacity-50"
        />
         <button
            onClick={handlePasteLoad}
            className="mt-2 w-full bg-teal-500 hover:bg-teal-600 text-white font-semibold py-2 px-4 rounded-md transition-colors disabled:bg-gray-500 disabled:opacity-50"
            disabled={!textInput.trim() || isProcessingFile}
          >
           Load Pasted Text
          </button>
      </div>

      {isProcessingFile && processingMessage && (
        <div className="mt-2 text-sm text-yellow-400 flex items-center">
          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-yellow-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {processingMessage}
        </div>
      )}

      {!isProcessingFile && fileName && processingMessage && (
         <div className={`mt-2 text-sm ${processingMessage?.includes("Error") ? 'text-red-400' : 'text-green-400'}`}>
          <i className={`fas ${processingMessage?.includes("Error") ? 'fa-exclamation-circle' : 'fa-check-circle'} mr-1`}></i>
          {processingMessage.startsWith("Loaded:") || processingMessage.startsWith("Pasted content loaded.") || processingMessage.startsWith("Error loading") ? processingMessage : `Loaded: ${fileName}`}
        </div>
      )}
       {!isProcessingFile && !fileName && processingMessage && (
         <div className={`mt-2 text-sm ${processingMessage?.includes("Error") ? 'text-red-400' : 'text-sky-300'}`}>
            <i className={`fas ${processingMessage?.includes("Error") ? 'fa-exclamation-circle' : 'fa-info-circle'} mr-1`}></i>
            {processingMessage}
         </div>
       )}
      
      {(textInput || fileName) && (
        <button
          onClick={handleClear}
          disabled={isProcessingFile}
          className="mt-2 w-full bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-md transition-colors disabled:bg-gray-500 disabled:opacity-50"
        >
         <i className="fas fa-times-circle mr-1"></i> Clear Document Context
        </button>
      )}
    </div>
  );
};

export default DocumentInput;
