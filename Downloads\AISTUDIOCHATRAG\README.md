# Gemini RAG Chat com Integração WhatsApp

Uma aplicação de chatbot RAG (Retrieval-Augmented Generation) usando a API Gemini com interface estilo WhatsApp e integração real com WhatsApp através do WPPConnect.

## Funcionalidades

- 🤖 **Chat com IA**: Converse com o Gemini AI
- 📄 **RAG (Retrieval-Augmented Generation)**: Faça upload de documentos para contexto
- 🔍 **Busca Google**: Obtenha informações atualizadas da web
- 📱 **Integração WhatsApp**: Envie e receba mensagens via WhatsApp real
- 🎤 **Entrada de Voz**: Suporte a mensagens de áudio
- 📊 **Interface Moderna**: Design responsivo estilo WhatsApp
- 💾 **Persistência de Dados**: Histórico completo com Supabase
- 🔍 **Busca Avançada**: Busque em conversas e mensagens WhatsApp
- 📈 **Estatísticas**: Monitore uso e atividade
- 🔄 **Auto-save**: Salva conversas automaticamente

## Pré-requisitos

- **Node.js** (versão 16 ou superior)
- **Google Chrome** (para o WPPConnect)
- **Chave API do Gemini**

## Instalação e Configuração

### 1. Clone e instale dependências

```bash
# Instalar dependências do frontend
npm install

# Instalar dependências do backend
cd backend
npm install
cd ..
```

### 2. Configurar variáveis de ambiente

**Frontend (.env.local):**
```env
GEMINI_API_KEY=sua_chave_api_do_gemini_aqui

# Supabase (Opcional - para persistência de dados)
VITE_SUPABASE_URL=https://seu-projeto.supabase.co
VITE_SUPABASE_ANON_KEY=sua_anon_key_aqui
```

**Backend (backend/.env):**
```env
PORT=3001
FRONTEND_URL=http://localhost:5173
SESSION_NAME=whatsapp-session

# Supabase (Opcional - para persistência de dados)
SUPABASE_URL=https://seu-projeto.supabase.co
SUPABASE_ANON_KEY=sua_anon_key_aqui
SUPABASE_SERVICE_ROLE_KEY=sua_service_role_key_aqui
```

> **📝 Nota**: O Supabase é opcional. Sem ele, a aplicação funciona normalmente, mas sem persistência de dados (histórico, contatos, etc.). Para configurar, veja [SUPABASE_SETUP.md](SUPABASE_SETUP.md).

### 3. Executar a aplicação

**Terminal 1 - Backend:**
```bash
cd backend
npm start
```

**Terminal 2 - Frontend:**
```bash
npm run dev
```

A aplicação estará disponível em: http://localhost:5173

## Funcionalidades de Persistência (Supabase)

### Com Supabase Configurado:
- ✅ **Histórico de Conversas**: Navegue por conversas anteriores
- ✅ **Auto-save**: Conversas salvas automaticamente
- ✅ **Busca Avançada**: Busque em mensagens e conversas
- ✅ **Contatos WhatsApp**: Lista de contatos salvos automaticamente
- ✅ **Histórico WhatsApp**: Todas as mensagens enviadas/recebidas
- ✅ **Estatísticas**: Métricas de uso e atividade
- ✅ **Backup Automático**: Dados seguros na nuvem

### Sem Supabase:
- ✅ **Chat Funcional**: Todas as funcionalidades de IA
- ✅ **WhatsApp**: Envio e recebimento em tempo real
- ✅ **RAG e Google Search**: Funcionam normalmente
- ❌ **Sem Persistência**: Dados perdidos ao recarregar
- ❌ **Sem Histórico**: Apenas sessão atual
- ❌ **Sem Busca**: Busca limitada à sessão atual

## Como usar a Integração WhatsApp

1. **Conectar**: Clique em "Conectar ao WhatsApp" na seção WhatsApp
2. **QR Code**: Escaneie o QR code que aparece com seu WhatsApp
3. **Aguardar**: Aguarde a confirmação da conexão
4. **Enviar**: Digite um número e envie mensagens do bot via WhatsApp

## Estrutura do Projeto

```
├── components/           # Componentes React
├── services/            # Serviços (Gemini, WhatsApp)
├── backend/             # Servidor Node.js com WPPConnect
│   ├── server.js        # Servidor principal
│   ├── logs/           # Logs do sistema
│   └── tokens/         # Sessões WhatsApp (gerado automaticamente)
├── types.ts            # Definições TypeScript
└── constants.ts        # Constantes da aplicação
```

## Logs e Monitoramento

Os logs são salvos automaticamente em `backend/logs/` com informações detalhadas sobre:
- Conexões WhatsApp
- Mensagens enviadas/recebidas
- Erros e status do sistema
- Atividade dos clientes Socket.IO

## Solução de Problemas

### WhatsApp não conecta
- Verifique se o Chrome está instalado
- Certifique-se de que não há outro WhatsApp Web aberto
- Verifique os logs em `backend/logs/`

### Erro de CORS
- Verifique se o `FRONTEND_URL` está correto no backend/.env
- Certifique-se de que ambos os serviços estão rodando

### Problemas com Socket.IO
- Verifique se a porta 3001 está livre
- Reinicie ambos os serviços

## Tecnologias Utilizadas

- **Frontend**: React, TypeScript, Vite, Socket.IO Client
- **Backend**: Node.js, Express, Socket.IO, WPPConnect
- **IA**: Google Gemini API
- **WhatsApp**: WPPConnect (Puppeteer-based)
