
import React, { useState, useEffect, useCallback } from 'react';
import { WhatsAppStatus, WhatsAppMessage as WhatsAppMessageType } from '../types';
import { whatsappService } from '../services/whatsappService';

interface WhatsAppControlsProps {
  lastBotResponseText: string | null;
}

const WhatsAppControls: React.FC<WhatsAppControlsProps> = ({ lastBotResponseText }) => {
  const [status, setStatus] = useState<WhatsAppStatus>('requires_backend');
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [currentActionInfo, setCurrentActionInfo] = useState<string | null>(null);
  const [recipient, setRecipient] = useState<string>('');
  const [isSending, setIsSending] = useState<boolean>(false);
  const [messages, setMessages] = useState<WhatsAppMessageType[]>([]);
  const [lastSentStatus, setLastSentStatus] = useState<string | null>(null);

  useEffect(() => {
    const unsubscribeStatus = whatsappService.onStatusChange((newStatus, newQrCode, newErrorOrInfo) => {
      setStatus(newStatus);
      setQrCode(newQrCode || null);
      if (newStatus === 'error' || (newStatus === 'connecting_qr' && newErrorOrInfo) || (newStatus === 'loading' && newErrorOrInfo) || newStatus === 'requires_backend') {
        setError(newErrorOrInfo || null);
        setCurrentActionInfo(null);
      } else {
        setError(null);
        setCurrentActionInfo(newErrorOrInfo || null);
      }
    });
    
    const unsubscribeMessages = whatsappService.onMessage((newMessage) => {
        setMessages(prev => [...prev, newMessage].sort((a,b) => a.timestamp.getTime() - b.timestamp.getTime()).slice(-10)); // Keep last 10
    });

    const initialState = whatsappService.getCurrentStatus();
    setStatus(initialState.status);
    setQrCode(initialState.qrCode || null);
    if (initialState.status === 'requires_backend' || initialState.error) {
         setError(initialState.error || "An initial error state detected.");
    }


    return () => {
      unsubscribeStatus();
      unsubscribeMessages();
    };
  }, []);

  const handleConnect = () => {
    setError(null);
    setCurrentActionInfo(null);
    setLastSentStatus(null);
    whatsappService.connect();
  };

  const handleSimulateQrScan = () => {
    setError(null);
    setCurrentActionInfo(null);
    whatsappService.confirmQrScanned_mock();
  };

  const handleDisconnect = () => {
    setError(null);
    setCurrentActionInfo(null);
    whatsappService.disconnect();
  };

  const handleSendMessage = async () => {
    if (!recipient.trim()) {
      setError("Recipient phone number is required.");
      return;
    }
     if (!lastBotResponseText) {
      setError("No bot response available to send.");
      return;
    }
    if (status !== 'connected') {
      setError("Must be connected to WhatsApp to send messages.");
      return;
    }

    setIsSending(true);
    setError(null);
    setCurrentActionInfo(null);
    setLastSentStatus(null);
    try {
      const success = await whatsappService.sendMessage(recipient, lastBotResponseText);
      if (success) {
        setLastSentStatus(`Message successfully sent to ${recipient} (simulated).`);
      } else {
        setError("Failed to send message (simulated). Not connected or other issue.");
      }
    } catch (e: any) {
      setError(`Error sending message: ${e.message}`);
    } finally {
      setIsSending(false);
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'connected': return 'text-green-400';
      case 'connecting_qr':
      case 'loading': return 'text-yellow-400';
      case 'error':
      case 'requires_backend': return 'text-red-400';
      case 'disconnected': return 'text-gray-400';
      default: return 'text-gray-200';
    }
  };
  
  const getStatusIcon = () => {
    switch (status) {
      case 'connected': return 'fas fa-check-circle';
      case 'connecting_qr': return 'fas fa-qrcode';
      case 'loading': return 'fas fa-spinner fa-spin';
      case 'error': return 'fas fa-exclamation-triangle';
      case 'requires_backend': return 'fas fa-cogs';
      case 'disconnected': return 'fas fa-plug';
      default: return 'fas fa-question-circle';
    }
  };

  return (
    <div className="bg-neutral-600 p-4 rounded-lg shadow space-y-3">
      <h2 className="text-lg font-semibold text-teal-400 mb-2 flex items-center">
        <i className="fab fa-whatsapp mr-2"></i> WhatsApp Integration
      </h2>
      
      <div className="text-xs text-amber-300 p-2 bg-amber-900 bg-opacity-50 rounded-md">
        <i className="fas fa-info-circle mr-1"></i> This is a frontend-only simulation. A backend server with 
        <a href="https://github.com/wppconnect-team/wppconnect" target="_blank" rel="noopener noreferrer" className="underline hover:text-amber-100"> wppconnect </a>
        is required for live WhatsApp functionality.
      </div>

      <div className="flex items-center space-x-2">
        <span className={`font-medium ${getStatusColor()}`}>Status:</span>
        <span className={`${getStatusColor()}`}>
            <i className={`${getStatusIcon()} mr-1`}></i>
            {status.replace(/_/g, ' ')}
        </span>
      </div>

      {error && <p className="text-sm text-red-400"><i className="fas fa-exclamation-circle mr-1"></i>Error: {error}</p>}
      {currentActionInfo && !error && <p className="text-sm text-sky-300"><i className="fas fa-info-circle mr-1"></i>{currentActionInfo}</p>}


      {status === 'requires_backend' && (
         <button
          onClick={handleConnect}
          className="w-full bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded-md transition-colors"
        >
          <i className="fas fa-play mr-1"></i> Attempt Mock Connection
        </button>
      )}

      {(status === 'disconnected' || (status === 'error' && !qrCode)) && ( // Show connect if fully disconnected or error without QR
        <button
          onClick={handleConnect}
          className="w-full bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-md transition-colors disabled:bg-gray-500"
        >
          <i className="fas fa-link mr-1"></i> Connect to WhatsApp (Mock)
        </button>
      )}
      
      {/* Disconnect button visible during loading, QR, or connected states */}
      {(status === 'loading' || status === 'connecting_qr' || status === 'connected') && (
        <button
          onClick={handleDisconnect}
          disabled={status === 'loading' && !qrCode} 
          className="w-full bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-md transition-colors disabled:bg-gray-500"
        >
          <i className="fas fa-unlink mr-1"></i> 
          {status === 'loading' && !qrCode ? 'Cancel Connection Attempt' : 'Disconnect (Mock)'}
        </button>
      )}

      {status === 'connecting_qr' && qrCode && (
        <div className="mt-2 p-2 border border-dashed border-gray-400 rounded text-center space-y-2">
          <p className="text-sm text-gray-300">Simulated QR Code:</p>
          <img src={qrCode} alt="Simulated QR Code" className="mx-auto bg-white p-1 rounded" />
          <button
            onClick={handleSimulateQrScan}
            className="w-full bg-sky-500 hover:bg-sky-600 text-white font-semibold py-2 px-4 rounded-md transition-colors"
          >
            <i className="fas fa-check-double mr-1"></i> Simulate QR Scan Success
          </button>
          <p className="text-xs text-gray-400 mt-1">Click above to simulate scanning the QR with your phone.</p>
        </div>
      )}

      {status === 'connected' && (
        <div className="mt-3 pt-3 border-t border-neutral-500 space-y-2">
          <h3 className="text-md font-semibold text-gray-200">Send Last Bot Response via WhatsApp</h3>
          {lastBotResponseText ? (
            <>
              <div>
                <label htmlFor="wa-recipient" className="block text-sm font-medium text-gray-300 mb-1">
                  Recipient Phone Number (e.g., 1XXXXXXXXXX)
                </label>
                <input
                  type="tel"
                  id="wa-recipient"
                  value={recipient}
                  onChange={(e) => setRecipient(e.target.value)}
                  placeholder="Enter WhatsApp number"
                  className="w-full p-2 border border-neutral-500 rounded-md bg-neutral-700 text-white placeholder-gray-400 focus:ring-2 focus:ring-teal-500"
                />
              </div>
              <p className="text-xs text-gray-400 italic">
                Will send: "{lastBotResponseText.substring(0, 50)}{lastBotResponseText.length > 50 ? '...' : ''}"
              </p>
              <button
                onClick={handleSendMessage}
                disabled={isSending || !recipient.trim() || !lastBotResponseText}
                className="w-full bg-teal-500 hover:bg-teal-600 text-white font-semibold py-2 px-4 rounded-md transition-colors disabled:bg-gray-500 disabled:opacity-70"
              >
                {isSending ? (
                  <><i className="fas fa-spinner fa-spin mr-1"></i> Sending...</>
                ) : (
                  <><i className="fab fa-whatsapp mr-1"></i> Send via WhatsApp</>
                )}
              </button>
              {lastSentStatus && <p className="text-sm text-green-400 mt-1"><i className="fas fa-check-circle mr-1"></i>{lastSentStatus}</p>}
            </>
          ) : (
            <p className="text-sm text-gray-400">Chat with the bot first to have a response to send.</p>
          )}
        </div>
      )}
       {messages.length > 0 && (
         <div className="mt-3 pt-3 border-t border-neutral-500 space-y-1">
            <h4 className="text-sm font-semibold text-gray-300 mb-1">WhatsApp Message Log (Mock):</h4>
            <div className="max-h-32 overflow-y-auto bg-neutral-800 p-2 rounded text-xs space-y-1">
                {messages.map(msg => (
                    <div key={msg.id} className={`p-1 rounded ${msg.isFromMe ? 'bg-teal-700 self-end ml-4 text-right' : 'bg-neutral-700 self-start mr-4'}`}>
                        <span className="font-bold">{msg.isFromMe ? "Me (Bot)" : msg.sender}: </span>
                        <span>{msg.text}</span>
                        <span className={`block text-neutral-400 text-[0.65rem] ${msg.isFromMe ? 'text-left' : 'text-right'}`}>{msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
                    </div>
                ))}
            </div>
         </div>
       )}
    </div>
  );
};

export default WhatsAppControls;
