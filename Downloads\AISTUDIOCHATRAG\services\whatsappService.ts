
import { WhatsAppStatus, WhatsAppMessage } from '../types';

// --- Mock Implementation ---
// In a real application, these functions would make API calls to your backend server
// where wppconnect is actually running.

let currentStatus: WhatsAppStatus = 'requires_backend';
let qrCodeData: string | null = null;
let connectionAttemptTimeout: NodeJS.Timeout | null = null;
let mockMessageInterval: NodeJS.Timeout | null = null;

// Simulate listeners for status changes and incoming messages
type StatusListener = (status: WhatsAppStatus, qrCode?: string | null, error?: string) => void;
type MessageListener = (message: WhatsAppMessage) => void;

const statusListeners: StatusListener[] = [];
const messageListeners: MessageListener[] = [];

const notifyStatusListeners = (status: WhatsAppStatus, qrCode?: string | null, error?: string) => {
  currentStatus = status;
  qrCodeData = qrCode || null;
  statusListeners.forEach(listener => listener(status, qrCode, error));
};

const notifyMessageListeners = (message: WhatsAppMessage) => {
  messageListeners.forEach(listener => listener(message));
};

export const whatsappService = {
  connect: async (): Promise<void> => {
    if (currentStatus === 'connected' || currentStatus === 'loading' || currentStatus === 'connecting_qr') {
      console.warn("Mock WhatsApp: Connection attempt ignored, already connected or in a connection process.");
      notifyStatusListeners(currentStatus, qrCodeData, "Already connected or in a connection process.");
      return;
    }
    notifyStatusListeners('loading', null, "Initiating mock connection...");
    
    console.log("Mock WhatsApp: Attempting to connect (simulating backend handshake)...");
    
    // Clear any previous timeouts
    if (connectionAttemptTimeout) clearTimeout(connectionAttemptTimeout);

    return new Promise((resolve) => {
      connectionAttemptTimeout = setTimeout(() => {
        // Simulate QR code step
        qrCodeData = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkAQMAAABN3N2gAAAABlBMVEX///8AAABVwtN+AAAAAXRSTlMAQObYZgAAAB5JREFUeNpjYBgFo4B8AAIDAYOBwSAwGFgAARhKAAEGANHRAPnpBArbAAAAAElFTkSuQmCC"; // Placeholder QR
        notifyStatusListeners('connecting_qr', qrCodeData, "Scan this mock QR code. Then click 'Simulate QR Scan Success'.");
        console.log("Mock WhatsApp: QR code generated (simulated). Waiting for scan confirmation from UI.");
        resolve(); 
      }, 2000); // Simulate delay to get QR
    });
  },

  confirmQrScanned_mock: async (): Promise<void> => {
    if (currentStatus !== 'connecting_qr') {
      console.warn("Mock WhatsApp: QR scan confirmation ignored, not in 'connecting_qr' state.");
      notifyStatusListeners(currentStatus, qrCodeData, "Error: Not in QR scanning state to confirm.");
      return;
    }
    
    notifyStatusListeners('loading', null, "QR scan confirmed. Finalizing mock connection...");
    console.log("Mock WhatsApp: QR scan confirmed by UI. Simulating final connection steps...");

    if (connectionAttemptTimeout) clearTimeout(connectionAttemptTimeout); // Clear any pending timeout from connect()

    return new Promise((resolve) => {
      setTimeout(() => {
        notifyStatusListeners('connected');
        console.log("Mock WhatsApp: Connected!");
        
        if (mockMessageInterval) clearInterval(mockMessageInterval); // Clear previous interval if any
        mockMessageInterval = setInterval(() => {
          const incomingMsg: WhatsAppMessage = {
            id: `wa-in-${Date.now()}`,
            text: `This is a simulated incoming message. Time: ${new Date().toLocaleTimeString()}`,
            sender: 'MockContact_7890',
            timestamp: new Date(),
            isFromMe: false,
          };
          notifyMessageListeners(incomingMsg);
        }, 15000); // New mock message every 15s
        resolve();
      }, 1500); // Simulate delay to "connect" after QR scan
    });
  },

  disconnect: async (): Promise<void> => {
    if (connectionAttemptTimeout) clearTimeout(connectionAttemptTimeout);
    if (mockMessageInterval) clearInterval(mockMessageInterval);
    
    const wasConnected = currentStatus === 'connected';
    notifyStatusListeners('disconnected', null, wasConnected ? "Disconnected by user." : "Connection attempt cancelled/disconnected.");
    console.log("Mock WhatsApp: Disconnected.");
    return Promise.resolve();
  },

  sendMessage: async (recipient: string, messageText: string): Promise<boolean> => {
    if (currentStatus !== 'connected') {
      console.warn("Mock WhatsApp: Cannot send message, not connected.");
      // Do not change status here, just report error for send attempt
      // notifyStatusListeners(currentStatus, qrCodeData, "Error: Cannot send message, not connected.");
      return Promise.resolve(false);
    }
    console.log(`Mock WhatsApp: Sending message to ${recipient}: "${messageText}"`);
    // Simulate sending message
    const sentMsg: WhatsAppMessage = {
      id: `wa-out-${Date.now()}`,
      text: messageText,
      sender: recipient, 
      timestamp: new Date(),
      isFromMe: true,
    };
    notifyMessageListeners(sentMsg);
    return new Promise(resolve => setTimeout(() => resolve(true), 500));
  },

  onStatusChange: (listener: StatusListener): (() => void) => {
    statusListeners.push(listener);
    const initialError = currentStatus === 'requires_backend' ? 'WhatsApp integration requires a backend server with wppconnect.' : undefined;
    listener(currentStatus, qrCodeData, initialError);
    return () => {
      const index = statusListeners.indexOf(listener);
      if (index > -1) statusListeners.splice(index, 1);
    };
  },
  
  onMessage: (listener: MessageListener): (() => void) => {
    messageListeners.push(listener);
    return () => {
      const index = messageListeners.indexOf(listener);
      if (index > -1) messageListeners.splice(index, 1);
    };
  },

  getCurrentStatus: (): { status: WhatsAppStatus, qrCode?: string|null, error?: string } => {
    const initialError = currentStatus === 'requires_backend' ? 'WhatsApp integration requires a backend server with wppconnect.' : undefined;
    return { 
        status: currentStatus, 
        qrCode: qrCodeData, 
        error: initialError
    };
  }
};
