
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { ChatMessage } from '../types';
import MessageBubble from './MessageBubble';
import Spinner from './Spinner';

interface ChatInterfaceProps {
  messages: ChatMessage[];
  onSendMessage: (payload: { text?: string; audio?: { mimeType: string; data: string; } }) => void;
  isLoading: boolean;
  apiKeyLoaded: boolean;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ messages, onSendMessage, isLoading, apiKeyLoaded }) => {
  const [userInput, setUserInput] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessingAudio, setIsProcessingAudio] = useState(false);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(scrollToBottom, [messages]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (userInput.trim() && !isLoading && apiKeyLoaded && !isRecording) {
      onSendMessage({ text: userInput });
      setUserInput('');
    }
  };

  const handleMicClick = async () => {
    if (!apiKeyLoaded) return;

    if (isRecording) {
      // Stop recording
      if (mediaRecorderRef.current && mediaRecorderRef.current.state === "recording") {
        mediaRecorderRef.current.stop();
      }
      setIsRecording(false);
      setIsProcessingAudio(true); // Indicate processing starts after stopping
    } else {
      // Start recording
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        mediaRecorderRef.current = new MediaRecorder(stream);
        audioChunksRef.current = [];

        mediaRecorderRef.current.ondataavailable = (event) => {
          audioChunksRef.current.push(event.data);
        };

        mediaRecorderRef.current.onstop = async () => {
          const MimeType = audioChunksRef.current.length > 0 && audioChunksRef.current[0].type ? audioChunksRef.current[0].type : 'audio/webm';
          const audioBlob = new Blob(audioChunksRef.current, { type: MimeType });
          
          const reader = new FileReader();
          reader.readAsDataURL(audioBlob);
          reader.onloadend = () => {
            const base64Audio = reader.result?.toString().split(',')[1];
            if (base64Audio) {
              onSendMessage({ text: userInput, audio: { mimeType: audioBlob.type || MimeType, data: base64Audio } });
              setUserInput(''); // Clear text input after sending with audio
            } else {
              console.error("Failed to convert audio to base64");
              alert("Error: Could not process recorded audio. Please try again.");
            }
            setIsProcessingAudio(false);
          };
          reader.onerror = () => {
            console.error("FileReader error during audio conversion.");
            alert("Error: Could not read recorded audio. Please try again.");
            setIsProcessingAudio(false);
          };
          // Clean up stream tracks
          stream.getTracks().forEach(track => track.stop());
        };

        mediaRecorderRef.current.start();
        setIsRecording(true);
        setIsProcessingAudio(false);
      } catch (err) {
        console.error("Error accessing microphone:", err);
        alert("Could not access microphone. Please ensure permission is granted and try again.");
        setIsRecording(false);
        setIsProcessingAudio(false);
      }
    }
  };


  return (
    <div className="flex flex-col h-full bg-black bg-opacity-20 backdrop-blur-sm">
      <div className="flex-1 overflow-y-auto p-4 space-y-2">
        {messages.map((msg) => (
          <MessageBubble key={msg.id} message={msg} />
        ))}
        {(isLoading || isProcessingAudio) && (
          <div className="flex justify-center py-2">
            <Spinner /> 
            {isProcessingAudio && !isLoading && <span className="text-sm text-gray-300 ml-2">Processing audio...</span>}
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>
      <form onSubmit={handleSubmit} className="p-4 border-t border-neutral-600 bg-neutral-700 bg-opacity-80">
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={userInput}
            onChange={(e) => setUserInput(e.target.value)}
            placeholder={
              apiKeyLoaded ? 
                isRecording ? "Recording... (text will accompany audio)" : "Type your message or use mic..." 
                : "API Key not loaded. Cannot send messages."
            }
            className="flex-1 p-3 rounded-full bg-neutral-600 text-white placeholder-gray-400 focus:ring-2 focus:ring-teal-500 focus:outline-none"
            disabled={isLoading || !apiKeyLoaded || isRecording || isProcessingAudio}
          />
          <button
            type="button"
            onClick={handleMicClick}
            disabled={isLoading || !apiKeyLoaded || isProcessingAudio}
            className={`p-3 w-12 h-12 flex items-center justify-center rounded-full text-white transition-colors
                        ${isRecording ? 'bg-red-500 hover:bg-red-600' : 'bg-sky-500 hover:bg-sky-600'}
                        disabled:bg-gray-500 disabled:cursor-not-allowed`}
            aria-label={isRecording ? "Stop recording" : "Start recording"}
          >
            {isProcessingAudio ? (
              <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : isRecording ? (
              <i className="fas fa-stop"></i>
            ) : (
              <i className="fas fa-microphone"></i>
            )}
          </button>
          <button
            type="submit"
            disabled={isLoading || !userInput.trim() || !apiKeyLoaded || isRecording || isProcessingAudio}
            className="p-3 w-12 h-12 flex items-center justify-center bg-teal-500 text-white rounded-full hover:bg-teal-600 disabled:bg-gray-500 disabled:cursor-not-allowed transition-colors"
            aria-label="Send message"
          >
            <i className="fas fa-paper-plane"></i>
          </button>
        </div>
      </form>
    </div>
  );
};

export default ChatInterface;
