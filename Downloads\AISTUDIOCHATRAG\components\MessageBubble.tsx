
import React from 'react';
import { ChatMessage } from '../types';
import SourceDisplay from './SourceDisplay';

interface MessageBubbleProps {
  message: ChatMessage;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  const isUser = message.sender === 'user';
  const isBot = message.sender === 'bot';
  const isSystem = message.sender === 'system';

  const baseBubbleClasses = "max-w-xl p-3 rounded-xl shadow-md break-words";
  const userBubbleClasses = "bg-teal-600 text-white self-end";
  const botBubbleClasses = "bg-neutral-600 text-white self-start";
  const systemBubbleClasses = "bg-yellow-600 bg-opacity-50 text-yellow-100 self-center text-xs italic p-2 text-center mx-auto w-full md:w-3/4";

  let bubbleClasses = baseBubbleClasses;
  if (isUser) bubbleClasses += ` ${userBubbleClasses}`;
  else if (isBot) bubbleClasses += ` ${botBubbleClasses}`;
  else if (isSystem) bubbleClasses += ` ${systemBubbleClasses}`;
  
  // Basic markdown-like formatting for bold and italics
  const formatText = (text: string) => {
    // Escape HTML to prevent XSS before applying markdown-like formatting
    const escapeHtml = (unsafe: string) => {
      return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
    };

    let formattedText = escapeHtml(text);
    
    // **bold**
    formattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    // *italic*
    formattedText = formattedText.replace(/\*(.*?)\*/g, '<em>$1</em>');
    // `code`
    formattedText = formattedText.replace(/`(.*?)`/g, '<code class="bg-gray-700 px-1 rounded text-sm">$1</code>');
    // ```code block``` - needs pre for newlines
    formattedText = formattedText.replace(/```([\s\S]*?)```/g, '<pre class="bg-gray-800 p-2 rounded text-sm my-1 overflow-x-auto whitespace-pre-wrap">$1</pre>');
    // newlines
    formattedText = formattedText.replace(/\n/g, '<br />');

    return { __html: formattedText };
  };


  return (
    <div className={`flex flex-col ${isUser ? 'items-end' : isSystem ? 'items-center' : 'items-start'}`}>
      <div className={bubbleClasses}>
        <p dangerouslySetInnerHTML={formatText(message.text)} />
        {message.sources && message.sources.length > 0 && isBot && (
          <SourceDisplay sources={message.sources} />
        )}
      </div>
      {!isSystem && (
        <span className="text-xs text-gray-400 mt-1 px-1">
          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </span>
      )}
    </div>
  );
};

export default MessageBubble;